# Shining C Music School App

A comprehensive music school management application built with Blazor WebAssembly and ASP.NET Core.

## Features

### Core Functionality
- **User Management**: Students, tutors, and administrators
- **Lesson Scheduling**: Interactive calendar with Syncfusion Scheduler
- **Role-Based Access**: Different permissions for students, tutors, and admins
- **Responsive Design**: Mobile-friendly interface with adaptive layouts

### Security Features
- **Authentication & Authorization**: Role-based access control
- **Session Timeout**: Automatic logout after configurable period of inactivity (default: 30 minutes)
- **Password Encryption**: Secure password storage
- **Environment-Based Configuration**: Secure configuration management

### Session Timeout
The application includes an automatic session timeout feature for enhanced security:

- **Default Timeout**: 30 minutes of inactivity
- **Activity Detection**: Monitors user interactions (mouse, keyboard, scroll, touch)
- **Automatic Logout**: Redirects to login page when timeout expires
- **Configurable**: Set via environment variable or configuration file

#### Configuration
```bash
# Environment Variable (Recommended for Production)
SESSION_TIMEOUT_MINUTES=30
```

```json
// appsettings.json
{
  "SessionTimeout": {
    "TimeoutMinutes": 30
  }
}
```

## Technology Stack

### Frontend
- **Blazor WebAssembly**: Client-side web application framework
- **Syncfusion Components**: Professional UI components for scheduling and data management
- **Bootstrap 5**: Responsive CSS framework
- **JavaScript Integration**: Custom activity tracking and mobile menu functionality

### Backend
- **ASP.NET Core Web API**: RESTful API services
- **Entity Framework Core**: Database access and ORM
- **IdentityServer4**: Authentication and authorization
- **SQL Server**: Database storage

### Deployment
- **Azure App Service**: Cloud hosting platform
- **Environment Variables**: Configuration management
- **CORS**: Cross-origin resource sharing configuration

## Project Structure

```
ShiningCMusicApp/
├── ShiningCMusicApp/          # Blazor WebAssembly Client
│   ├── Components/            # Reusable Blazor components
│   ├── Pages/                 # Application pages
│   ├── Services/              # Client-side services
│   ├── Layout/                # Application layout components
│   └── wwwroot/               # Static files and assets
├── ShiningCMusicApi/          # ASP.NET Core Web API
│   ├── Controllers/           # API controllers
│   ├── Services/              # Business logic services
│   └── Infrastructure/        # Database and configuration
├── ShiningCMusicCommon/       # Shared models and utilities
└── Documentation/             # Project documentation
```

## Getting Started

### Prerequisites
- .NET 8.0 SDK
- SQL Server (LocalDB or full instance)
- Visual Studio 2022 or VS Code

### Configuration

#### Database Connection
Set the connection string in `appsettings.json` or environment variable:
```json
{
  "ConnectionStrings": {
    "MusicSchool": "Server=(localdb)\\mssqllocaldb;Database=MusicSchool;Trusted_Connection=true"
  }
}
```

#### Session Timeout (Optional)
```json
{
  "SessionTimeout": {
    "TimeoutMinutes": 30
  }
}
```

### Running the Application

1. **Clone the repository**
2. **Restore packages**: `dotnet restore`
3. **Update database**: Run SQL scripts in `ShiningCMusicApi/SQL/`
4. **Start API**: `dotnet run --project ShiningCMusicApi`
5. **Start Client**: `dotnet run --project ShiningCMusicApp`

### Default Users
- **Admin**: admin/password
- **Tutor**: tutor/password  
- **Student**: student/password

## Documentation

### Feature Documentation
- [Session Timeout Implementation](Documentation/SessionTimeout_Implementation.md) - Complete session timeout documentation
- [Session Timeout Quick Reference](Documentation/SessionTimeout_QuickReference.md) - Quick setup guide
- [Mobile Improvements](Documentation/MOBILE_IMPROVEMENTS.md) - Mobile interface enhancements
- [User Role Management](Documentation/UserRole_ID_Management.md) - Role-based access control
- [Password Validation](Documentation/Client_Password_Validation.md) - Client-side validation

### API Documentation
- Swagger UI available at `/swagger` when running in development mode
- RESTful API endpoints for all major functionality
- Authentication via IdentityServer4 OAuth2/OpenID Connect

## Deployment

### Azure App Service
1. **Create App Service** in Azure Portal
2. **Set Environment Variables**:
   - `ConnectionStrings_MusicSchool`: Database connection string
   - `SESSION_TIMEOUT_MINUTES`: Session timeout duration
   - `API_BASE_URL`: API base URL
3. **Deploy** via Visual Studio or Azure DevOps

### Environment Variables
- `SESSION_TIMEOUT_MINUTES`: Session timeout in minutes (default: 30)
- `API_BASE_URL`: Base URL for API endpoints
- `ConnectionStrings_MusicSchool`: Database connection string
- `SYNCFUSION_LICENSE`: Syncfusion license key

## Security Considerations

- **Session Management**: Automatic timeout prevents unauthorized access
- **Password Security**: Encrypted password storage
- **Role-Based Access**: Granular permissions by user role
- **CORS Configuration**: Controlled cross-origin access
- **Environment Variables**: Sensitive configuration via environment variables

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is proprietary software for Shining C Music School.

## Support

For technical support or questions, please contact the development team.
