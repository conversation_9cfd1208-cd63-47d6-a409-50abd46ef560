# Session Timeout Implementation

## Overview

The Shining C Music App now includes an automatic session timeout feature that logs users out after a configurable period of inactivity. This enhances security by ensuring that unattended sessions are automatically terminated.

## Features

- **Configurable Timeout Duration**: Default 30 minutes, customizable via environment variables or configuration files
- **User Activity Detection**: Monitors mouse, keyboard, scroll, touch, and click events
- **Automatic Logout**: Logs out user and redirects to login page when timeout expires
- **Activity-Based Reset**: Timeout resets whenever user activity is detected
- **Performance Optimized**: Activity reporting is throttled to prevent excessive API calls
- **Environment Variable Support**: Can be configured for different deployment environments

## Configuration

### Environment Variable (Recommended for Production)
```bash
SESSION_TIMEOUT_MINUTES=30
```

### Configuration Files

#### API Configuration (ShiningCMusicApi/appsettings.json)
```json
{
  "SessionTimeout": {
    "TimeoutMinutes": 30
  }
}
```

#### Client Configuration (ShiningCMusicApp/wwwroot/appsettings.json)
```json
{
  "SessionTimeout": {
    "TimeoutMinutes": 30
  }
}
```

### Configuration Priority
1. **Environment Variable**: `SESSION_TIMEOUT_MINUTES` (highest priority)
2. **Configuration File**: `SessionTimeout:TimeoutMinutes` in appsettings.json
3. **Default Value**: 30 minutes (fallback)

## Architecture

### Components

#### 1. SessionTimeoutService
**Location**: `ShiningCMusicApp/Services/SessionTimeoutService.cs`

**Responsibilities**:
- Manages session timeout timer
- Handles user activity detection
- Triggers automatic logout
- Communicates with JavaScript for activity tracking

**Key Methods**:
- `InitializeAsync()`: Sets up the service and loads configuration
- `StartTimeoutAsync()`: Starts the timeout timer
- `StopTimeoutAsync()`: Stops the timeout timer
- `ResetTimeoutAsync()`: Resets the timer due to user activity
- `OnUserActivity()`: JSInvokable method called from JavaScript

#### 2. JavaScript Activity Tracking
**Location**: `ShiningCMusicApp/wwwroot/js/app.js`

**Functionality**:
- Monitors user interaction events
- Throttles activity reporting (every 30 seconds)
- Communicates with Blazor service via JSInterop

**Tracked Events**:
- `mousedown`, `mousemove`
- `keypress`
- `scroll`
- `touchstart`
- `click`

#### 3. SessionTimeoutInitializer Component
**Location**: `ShiningCMusicApp/Components/SessionTimeoutInitializer.razor`

**Purpose**:
- Initializes session timeout service on app startup
- Connects authentication provider with session timeout service
- Starts timeout for existing authenticated users

#### 4. Authentication Integration
**Location**: `ShiningCMusicApp/Services/CustomAuthenticationStateProvider.cs`

**Enhancements**:
- Starts session timeout on successful login
- Stops session timeout on logout
- Integrates with session timeout service lifecycle

## Implementation Details

### File Changes

#### New Files Created
1. `ShiningCMusicApp/Services/SessionTimeoutService.cs` - Core timeout service
2. `ShiningCMusicApp/Components/SessionTimeoutInitializer.razor` - Initialization component
3. `ShiningCMusicApp/Documentation/SessionTimeout_Implementation.md` - This documentation

#### Modified Files
1. `ShiningCMusicCommon/Models/AppConfiguration.cs` - Added SessionTimeoutMinutes property
2. `ShiningCMusicApi/Controllers/ConfigurationController.cs` - Added timeout configuration reading
3. `ShiningCMusicApi/appsettings.json` - Added SessionTimeout section
4. `ShiningCMusicApp/wwwroot/appsettings.json` - Added SessionTimeout section
5. `ShiningCMusicApp/wwwroot/js/app.js` - Added activity tracking functionality
6. `ShiningCMusicApp/Services/CustomAuthenticationStateProvider.cs` - Integrated session timeout
7. `ShiningCMusicApp/Program.cs` - Registered session timeout service
8. `ShiningCMusicApp/Layout/MainLayout.razor` - Added initializer component

### Service Registration
```csharp
// In Program.cs
builder.Services.AddScoped<ISessionTimeoutService, SessionTimeoutService>();
```

### JavaScript Integration
```javascript
// Activity tracking setup
window.sessionTimeout = {
    dotNetRef: null,
    isInitialized: false,
    lastActivity: Date.now(),
    activityThrottle: 30000, // 30 seconds

    initialize: function(dotNetReference) {
        this.dotNetRef = dotNetReference;
        this.isInitialized = true;
        this.setupActivityListeners();
    },

    onActivity: function() {
        // Throttled activity reporting to Blazor
        if (now - this.lastActivity > this.activityThrottle) {
            this.dotNetRef.invokeMethodAsync('OnUserActivity');
        }
    }
};
```

## Usage

### For Administrators

#### Setting Timeout in Azure App Service
1. Navigate to Azure App Service → Configuration → Application Settings
2. Add new setting: `SESSION_TIMEOUT_MINUTES` = `30` (or desired minutes)
3. Save and restart the application

#### Setting Timeout in Local Development
1. Modify `appsettings.json` in the API project:
   ```json
   {
     "SessionTimeout": {
       "TimeoutMinutes": 15
     }
   }
   ```
2. Restart the application

### For Users

#### Behavior
- **Active Session**: Timer resets with any user interaction
- **Idle Session**: After configured timeout, user is automatically logged out
- **Warning**: No warning is currently displayed (can be added as future enhancement)
- **Redirect**: User is redirected to login page after timeout

#### Monitored Activities
- Mouse movements and clicks
- Keyboard input
- Page scrolling
- Touch interactions (mobile)

## Testing

### Manual Testing
1. **Login** to the application
2. **Check Console**: Look for "Session timeout started" message
3. **Test Activity Reset**: Interact with the app and check for "Session timeout reset" messages
4. **Test Timeout**: Leave the app idle for the configured duration
5. **Verify Logout**: Confirm automatic logout and redirect to login page

### Configuration Testing
1. **Environment Variable**: Set `SESSION_TIMEOUT_MINUTES=5` and test 5-minute timeout
2. **Configuration File**: Modify appsettings.json and test different values
3. **Default Fallback**: Remove all configuration and verify 30-minute default

### Console Logging
The implementation includes detailed console logging for debugging:
- Session timeout initialization
- Timer start/stop events
- Activity detection
- Timeout expiration
- Configuration loading

## Security Considerations

### Benefits
- **Automatic Session Termination**: Prevents unauthorized access to unattended sessions
- **Configurable Duration**: Allows adjustment based on security requirements
- **Activity-Based Reset**: Maintains usability while ensuring security

### Recommendations
- **Production Timeout**: Consider 15-30 minutes for production environments
- **Development Timeout**: Use longer timeouts (60+ minutes) for development
- **Sensitive Environments**: Use shorter timeouts (5-15 minutes) for high-security scenarios

## Future Enhancements

### Potential Improvements
1. **Warning Dialog**: Show warning before automatic logout
2. **Extend Session**: Allow users to extend their session
3. **Different Timeouts by Role**: Shorter timeouts for admin users
4. **Session Activity Logging**: Log session timeout events for audit purposes
5. **Configurable Warning Time**: Warn users X minutes before timeout

### Implementation Notes
- The current implementation prioritizes simplicity and security
- Activity throttling prevents performance issues
- Environment variable support enables easy deployment configuration
- Clean separation of concerns between services and components

## Troubleshooting

### Common Issues

#### Session Not Timing Out
- Check console for initialization messages
- Verify configuration is loaded correctly
- Ensure JavaScript is not blocked

#### Immediate Logout
- Check if timeout value is too low
- Verify activity detection is working
- Check for JavaScript errors in console

#### Configuration Not Loading
- Verify environment variable name: `SESSION_TIMEOUT_MINUTES`
- Check appsettings.json syntax
- Ensure API configuration endpoint is accessible

### Debug Information
Enable detailed logging by checking browser console for:
- "Session timeout initialized with X minutes timeout"
- "Session timeout started - will expire in X minutes"
- "Session timeout reset due to user activity"
- "Session timeout elapsed - logging out user"
