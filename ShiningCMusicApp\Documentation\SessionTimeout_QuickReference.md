# Session Timeout - Quick Reference

## Configuration

### Environment Variable (Production)
```bash
SESSION_TIMEOUT_MINUTES=30
```

### Configuration File (Development)
```json
{
  "SessionTimeout": {
    "TimeoutMinutes": 30
  }
}
```

## Key Features
- ✅ **Default**: 30 minutes timeout
- ✅ **Activity Detection**: Mouse, keyboard, scroll, touch events
- ✅ **Automatic Logout**: Redirects to login page
- ✅ **Configurable**: Environment variables or appsettings.json
- ✅ **Performance Optimized**: Throttled activity reporting

## Files Modified/Created

### New Files
- `Services/SessionTimeoutService.cs` - Core timeout logic
- `Components/SessionTimeoutInitializer.razor` - Initialization component
- `Documentation/SessionTimeout_Implementation.md` - Full documentation

### Modified Files
- `Models/AppConfiguration.cs` - Added SessionTimeoutMinutes
- `Controllers/ConfigurationController.cs` - Added timeout config
- `appsettings.json` (API & Client) - Added SessionTimeout section
- `wwwroot/js/app.js` - Added activity tracking
- `CustomAuthenticationStateProvider.cs` - Integrated timeout
- `Program.cs` - Registered service
- `MainLayout.razor` - Added initializer

## Testing

### Quick Test
1. Login to the app
2. Check browser console for "Session timeout started"
3. Leave app idle for configured time
4. Verify automatic logout

### Configuration Test
```bash
# Set 5-minute timeout for testing
SESSION_TIMEOUT_MINUTES=5
```

## Console Messages
- `Session timeout initialized with X minutes timeout`
- `Session timeout started - will expire in X minutes`
- `Session timeout reset due to user activity`
- `Session timeout elapsed - logging out user`

## Deployment

### Azure App Service
1. Go to Configuration → Application Settings
2. Add: `SESSION_TIMEOUT_MINUTES` = `30`
3. Save and restart

### Local Development
Modify `appsettings.json`:
```json
{
  "SessionTimeout": {
    "TimeoutMinutes": 15
  }
}
```

## Security Recommendations
- **Production**: 15-30 minutes
- **Development**: 60+ minutes  
- **High Security**: 5-15 minutes
